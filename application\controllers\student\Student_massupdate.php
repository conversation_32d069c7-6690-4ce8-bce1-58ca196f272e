<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  02 May 2018
 *
 * Description: Controller for Mass Update.
 *
 * Requirements: PHP5 or above
 *
 */

 /**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class Student_massupdate extends CI_Controller {
  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT_MASSUPDATE.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('student/Student_massupdate_model');
    $this->load->model('report/Student_report_model');
    $this->config->load('form_elements');
    $this->load->model('student/Student_Model');
  }   

  public function index() {
    $data['classList'] = $this->Student_Model->getclass();
    //$data['columnList'] = $this->Student_massupdate_model->getcolumns('student');
    $columnList = [
      // [
      //   'displayName'=>'Student First Name',
      //   'columnNameWithTable'=>'sa.first_name',
      //   'columnName'=>'first_name',
      //   'varName'=>'sFirstName',
      //   'table'=>'student_admission',
      //   'index'=>'1',
      //   'required'=>false,
      //   'displayType'=>'text',
      //   'dataType'=>'string',
      //   'info'=>'personal_info'
      // ],
      [
        'displayName'=>'Student Last Name',
        'columnNameWithTable'=>'sa.last_name',
        'columnName'=>'last_name',
        'varName'=>'sLastName',
        'table'=>'student_admission',
        'index'=>'2',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student Date of Birth',
        'columnNameWithTable'=>'sa.dob',
        'columnName'=>'dob',
        'varName'=>'sDOB',
        'table'=>'student_admission',
        'index'=>'3',
        'required'=>false,
        'displayType'=>'datetimepicker',
        'dataType'=>'date',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Preferred Contact Number',
        'columnNameWithTable'=>'sa.preferred_contact_no',
        'columnName'=>'preferred_contact_no',
        'varName'=>'sPreferredContactNo',
        'table'=>'student_admission',
        'index'=>'4',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student Email',
        'columnNameWithTable'=>'sa.email',
        'columnName'=>'email',
        'varName'=>'sEmail',
        'table'=>'student_admission',
        'index'=>'5',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student Gender',
        'columnNameWithTable'=>'sa.gender',
        'columnName'=>'gender',
        'varName'=>'sGender',
        'optionArrName'=>'genderOptions',
        'table'=>'student_admission',
        'index'=>'6',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student Aadhar Number',
        'columnNameWithTable'=>'sa.aadhar_no',
        'columnName'=>'aadhar_no',
        'varName'=>'sAadharNo',
        'table'=>'student_admission',
        'index'=>'7',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Roll number',
        'columnNameWithTable'=>'sy.roll_no',
        'columnName'=>'roll_no',
        'varName'=>'sRollNo',
        'table'=>'student_year',
        'index'=>'8',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Admission Number',
        'columnNameWithTable'=>'sa.admission_no',
        'columnName'=>'admission_no',
        'varName'=>'sAdmissionNo',
        'table'=>'student_admission',
        'index'=>'9',
        'required'=>true,
        'displayType'=>'text',
        'dataType'=>'unique', //This will trigger a unique check before update.
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Registration Number',
        'columnNameWithTable'=>'sa.registration_no',
        'columnName'=>'registration_no',
        'varName'=>'sRegistrationNo',
        'table'=>'student_admission',
        'index'=>'10',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Enrollment Number',
        'columnNameWithTable'=>'sa.enrollment_number',
        'columnName'=>'enrollment_number',
        'varName'=>'sEnrollmentNumber',
        'table'=>'student_admission',
        'index'=>'11',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'unique', //This will trigger a unique check before update.
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student SATS Number',
        'columnNameWithTable'=>'sa.sts_number',
        'columnName'=>'sts_number',
        'varName'=>'sSTSNumber',
        'table'=>'student_admission',
        'index'=>'12',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Section',
        'columnNameWithTable'=>'sy.class_section_id',
        'columnName'=>'class_section_id',
        'varName'=>'sClassSection',
        'optionArrName'=>'csOptions',
        'table'=>'student_year',
        'index'=>'13',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Class Admitted To',
        'columnNameWithTable'=>'sa.class_admitted_to',
        'columnName'=>'class_admitted_to',
        'varName'=>'sClassadmittedto',
        'optionArrName'=>'sClassAdmitted',
        'table'=>'student_admission',
        'index'=>'14',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Medium',
        'columnNameWithTable'=>'sy.medium',
        'columnName'=>'medium',
        'varName'=>'sMedium',
        'optionArrName'=>'mediumOptions',
        'table'=>'student_year',
        'index'=>'15',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Academic Year Of Joining',
        'columnNameWithTable'=>'sa.admission_acad_year_id',
        'columnName'=>'admission_acad_year_id',
        'varName'=>'sAdmissionAcadYearId',
        'optionArrName'=>'acadyearOptions',
        'table'=>'student_admission',
        'index'=>'16',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Birth Taluk/District',
        'columnNameWithTable'=>'sa.birth_taluk',
        'columnName'=>'birth_taluk',
        'varName'=>'sBirthTaluk',
        'table'=>'student_admission',
        'index'=>'17',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Birth State',
        'columnNameWithTable'=>'sa.birth_district',
        'columnName'=>'birth_district',
        'varName'=>'sBirthDistrict',
        'table'=>'student_admission',
        'index'=>'18',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Nationality',
        'columnNameWithTable'=>'sa.nationality',
        'columnName'=>'nationality',
        'varName'=>'sNationality',
        'optionArrName'=>'nationalityOptions',
        'table'=>'student_admission',
        'index'=>'19',
        'required'=>false,
        'displayType'=>'combobox',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Religion',
        'columnNameWithTable'=>'sa.religion',
        'columnName'=>'religion',
        'varName'=>'sReligion',
        'optionArrName'=>'religionOptions',
        'table'=>'student_admission',
        'index'=>'20',
        'required'=>false,
        'displayType'=>'combobox',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student Mother Tongue',
        'columnNameWithTable'=>'sa.mother_tongue',
        'columnName'=>'mother_tongue',
        'varName'=>'sMotherTongue',
        'optionArrName'=>'motherTongueOptions',
        'table'=>'student_admission',
        'index'=>'21',
        'required'=>false,
        'displayType'=>'combobox',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Caste',
        'columnNameWithTable'=>'sa.caste',
        'columnName'=>'caste',
        'varName'=>'sCaste',
        'optionArrName'=>'casteOptions',
        'table'=>'student_admission',
        'index'=>'22',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Third Language Choice',
        'columnNameWithTable'=>'sa.third_language_choice',
        'columnName'=>'third_language_choice',
        'varName'=>'sLang910',
        'optionArrName'=>'subLangOptions',
        'table'=>'student_admission',
        'index'=>'23',
        'required'=>false,
        'displayType'=>'combobox',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Second Language Choice',
        'columnNameWithTable'=>'sa.second_language_choice',
        'columnName'=>'second_language_choice',
        'varName'=>'sLang68',
        'optionArrName'=>'subLangOptions',
        'table'=>'student_admission',
        'index'=>'24',
        'required'=>false,
        'displayType'=>'combobox',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'House',
        'columnNameWithTable'=>'sy.student_house',
        'columnName'=>'student_house',
        'varName'=>'sStudentHouse',
        'optionArrName'=>'houseOptions',
        'table'=>'student_year',
        'index'=>'25',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Date of Joining',
        'columnNameWithTable'=>'sa.date_of_joining',
        'columnName'=>'date_of_joining',
        'varName'=>'sDOJ',
        'table'=>'student_admission',
        'index'=>'26',
        'required'=>false,
        'displayType'=>'datetimepicker',
        'dataType'=>'date',
        'info'=>'school_info'
      ],
      // [
      //   'displayName'=>'Course/Combination',
      //   'columnNameWithTable'=>'sy.combination',
      //   'columnName'=>'combination',
      //   'varName'=>'sCombination',
      //   'optionArrName'=>'combOption',
      //   'table'=>'student_year',
      //   'index'=>'27',
      //   'displayType'=>'combobox',
      //   'dataType'=>'string',
      //   'info'=>'school_info'
      // ],
      [
        'displayName'=>'Course/Combination',
        'columnNameWithTable'=>'sy.combination_id',
        'columnName'=>'combination_id',
        'varName'=>'sCombination_id',
        'optionArrName'=>'combinationOption',
        'table'=>'student_year',
        'index'=>'27',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Quota',
        'columnNameWithTable'=>'sa.quota',
        'columnName'=>'quota',
        'varName'=>'quota',
        'optionArrName'=>'quota',
        'table'=>'student_admission',
        'index'=>'28',
        'required'=>false,'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Attempt',
        'columnNameWithTable'=>'sa.attempt',
        'columnName'=>'attempt',
        'varName'=>'attempt',
        'optionArrName'=>'attempt',
        'table'=>'student_admission',
        'index'=>'29',
        'required'=>false,'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Category',
        'columnNameWithTable'=>'sa.category',
        'columnName'=>'category',
        'varName'=>'category',
        'optionArrName'=>'category',
        'table'=>'student_admission',
        'index'=>'30',
        'required'=>false,'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      // [
      //   'displayName'=>'Email Id',
      //   'columnNameWithTable'=>'sa.email',
      //   'columnName'=>'email',
      //   'varName'=>'email',
      //   'table'=>'student_admission',
      //   'index'=>'31',
      //   'required'=>false,
      //   'displayType'=>'text',
      //   'dataType'=>'string',
      //   'info'=>'personal_info'
      // ],
      [
        'displayName'=>'Email Password',
        'columnNameWithTable'=>'sa.email_password',
        'columnName'=>'email_password',
        'varName'=>'email_password',
        'table'=>'student_admission',
        'index'=>'32',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Semester',
        'columnNameWithTable'=>'sy.semester',
        'columnName'=>'semester',
        'varName'=>'sSemester',
        'optionArrName'=>'semseterList',
        'table'=>'student_year',
        'index'=>'33',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Blood Group',
        'columnNameWithTable'=>'sa.blood_group',
        'columnName'=>'blood_group',
        'varName'=>'blood_group',
        'optionArrName'=>'blood_group',
        'table'=>'student_admission',
        'index'=>'34',
        'required'=>false,'displayType'=>'combobox',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],

      [
        'displayName'=>'Student Board',
        'columnNameWithTable'=>'sy.board',
        'columnName'=>'board',
        'varName'=>'sBoard',
        'optionArrName'=>'boardOptions',
        'table'=>'student_year',
        'index'=>'35',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Boarding',
        'columnNameWithTable'=>'sy.boarding',
        'columnName'=>'boarding',
        'varName'=>'sBoarding',
        'optionArrName'=>'boardingOptions',
        'table'=>'student_year',
        'index'=>'36',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Extra Curricular Activities',
        'columnNameWithTable'=>'sa.extracurricular_activities',
        'columnName'=>'extracurricular_activities',
        'varName'=>'sExtracurricular',
        'table'=>'student_admission',
        'index'=>'37',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Father First Name',
        'columnNameWithTable'=>'f.first_name',
        'columnName'=>'first_name',
        'varName'=>'fFirstName',
        'table'=>'father',
        'index'=>'38',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Last Name',
        'columnNameWithTable'=>'f.last_name',
        'columnName'=>'last_name',
        'varName'=>'fLastName',
        'table'=>'father',
        'index'=>'39',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Aadhar Number',
        'columnNameWithTable'=>'f.aadhar_no',
        'columnName'=>'aadhar_no',
        'varName'=>'fAadharNo',
        'table'=>'father',
        'index'=>'40',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Qualification',
        'columnNameWithTable'=>'f.qualification',
        'columnName'=>'qualification',
        'varName'=>'fQualification',
        'table'=>'father',
        'index'=>'41',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Occupation',
        'columnNameWithTable'=>'f.occupation',
        'columnName'=>'occupation',
        'varName'=>'fOccupation',
        'table'=>'father',
        'index'=>'42',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Company',
        'columnNameWithTable'=>'f.company',
        'columnName'=>'company',
        'varName'=>'fCompany',
        'table'=>'father',
        'index'=>'43',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Annual Income',
        'columnNameWithTable'=>'f.annual_income',
        'columnName'=>'annual_income',
        'varName'=>'fAnnualIncome',
        'table'=>'father',
        'index'=>'44',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Mobile Number',
        'columnNameWithTable'=>'f.mobile_no',
        'columnName'=>'mobile_no',
        'varName'=>'fMobileNumber',
        'table'=>'father',
        'index'=>'45',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father EMail',
        'columnNameWithTable'=>'f.email',
        'columnName'=>'email',
        'varName'=>'femail',
        'table'=>'father',
        'index'=>'46',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Mother Tongue',
        'columnNameWithTable'=>'f.mother_tongue',
        'columnName'=>'mother_tongue',
        'varName'=>'fMotherTongue',
        'optionArrName'=>'motherTongueOptions',
        'table'=>'father',
        'index'=>'47',
        'required'=>false,
        'displayType'=>'combobox',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Designation',
        'columnNameWithTable'=>'f.designation',
        'columnName'=>'designation',
        'varName'=>'fDesignation',
        'table'=>'father',
        'index'=>'48',
        'required'=>false,'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father PAN number',
        'columnNameWithTable'=>'f.pan_number',
        'columnName'=>'pan_number',
        'varName'=>'fPanNumber',
        'table'=>'father',
        'index'=>'49',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother First Name',
        'columnNameWithTable'=>'m.first_name',
        'columnName'=>'first_name',
        'varName'=>'mFirstName',
        'table'=>'mother',
        'index'=>'50',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Last Name',
        'columnNameWithTable'=>'m.last_name',
        'columnName'=>'last_name',
        'varName'=>'mLastName',
        'table'=>'mother',
        'index'=>'51',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Aadhar Number',
        'columnNameWithTable'=>'m.aadhar_no',
        'columnName'=>'aadhar_no',
        'varName'=>'mAadharNo',
        'table'=>'mother',
        'index'=>'52',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Qualification',
        'columnNameWithTable'=>'m.qualification',
        'columnName'=>'qualification',
        'varName'=>'mQualification',
        'table'=>'mother',
        'index'=>'53',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Occupation',
        'columnNameWithTable'=>'m.occupation',
        'columnName'=>'occupation',
        'varName'=>'mOccupation',
        'table'=>'mother',
        'index'=>'54',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Company',
        'columnNameWithTable'=>'m.company',
        'columnName'=>'company',
        'varName'=>'mCompany',
        'table'=>'mother',
        'index'=>'55',
        'required'=>false,'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Annual Income',
        'columnNameWithTable'=>'m.annual_income',
        'columnName'=>'annual_income',
        'varName'=>'mAnnualIncome',
        'table'=>'mother',
        'index'=>'56',
        'required'=>false,'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Mobile Number',
        'columnNameWithTable'=>'m.mobile_no',
        'columnName'=>'mobile_no',
        'varName'=>'mMobileNumber',
        'table'=>'mother',
        'index'=>'57',
        'required'=>false,'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother EMail',
        'columnNameWithTable'=>'m.email',
        'columnName'=>'email',
        'varName'=>'memail',
        'table'=>'mother',
        'index'=>'58',
        'required'=>false,'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Mother Tongue',
        'columnNameWithTable'=>'m.mother_tongue',
        'columnName'=>'mother_tongue',
        'varName'=>'mMotherTongue',
        'optionArrName'=>'motherTongueOptions',
        'table'=>'mother',
        'index'=>'59',
        'required'=>false,'displayType'=>'combobox',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Designation',
        'columnNameWithTable'=>'m.designation',
        'columnName'=>'designation',
        'varName'=>'mDesignation',
        'table'=>'mother',
        'index'=>'60',
        'required'=>false,'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother PAN number',
        'columnNameWithTable'=>'m.pan_number',
        'columnName'=>'pan_number',
        'varName'=>'mPanNumber',
        'table'=>'mother',
        'index'=>'61',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'After School Sports',
        'columnNameWithTable'=>'sy.after_school_sport',
        'columnName'=>'after_school_sport',
        'varName'=>'sAfterSchoolSport',
        'optionArrName'=>'assOptions',
        'table'=>'student_year',
        'index'=>'62',
        'required'=>false,'displayType'=>'combobox',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'After School Sports (Days)',
        'columnNameWithTable'=>'sy.after_school_sport_days',
        'columnName'=>'after_school_sport_days',
        'varName'=>'sAfterSchoolSport_days',
        'table'=>'student_year',
        'index'=>'63',
        'required'=>false,'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      
      [
        'displayName'=>'Student Admission type',
        'columnNameWithTable'=>'sy.admission_type',
        'columnName'=>'admission_type',
        'varName'=>'admType',
        'optionArrName'=>'admissionType',
        'table'=>'student_year',
        'index'=>'64',
        'required'=>false,'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'RTE',
        'columnNameWithTable'=>'sy.is_rte',
        'columnName'=>'is_rte',
        'varName'=>'isRTE',
        'optionArrName'=>'is_rte',
        'table'=>'student_year',
        'index'=>'65',
        'required'=>false,'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Preferred Parent',
        'columnNameWithTable'=>'sa.preferred_parent',
        'columnName'=>'preferred_parent',
        'varName'=>'sPreferredParent',
        'optionArrName'=>'preferredParentOptions',
        'table'=>'student_admission',
        'index'=>'66',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Alpha Roll Number',
        'columnNameWithTable'=>'sy.alpha_rollnum',
        'columnName'=>'alpha_rollnum',
        'varName'=>'sAlpharollnum',
        'table'=>'student_year',
        'index'=>'67',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'RF Id Number',
        'columnNameWithTable'=>'sa.rfid_number',
        'columnName'=>'rfid_number',
        'varName'=>'sRfidnum',
        'table'=>'student_admission',
        'index'=>'68',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Nick name',
        'columnNameWithTable'=>'sa.nick_name',
        'columnName'=>'nick_name',
        'varName'=>'nickName',
        'table'=>'student_admission',
        'index'=>'69',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student Indian Visa Number',
        'columnNameWithTable'=>'sa.student_indian_visa_number',
        'columnName'=>'student_indian_visa_number',
        'varName'=>'indianVisaNum',
        'table'=>'student_admission',
        'index'=>'70',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student Indian Visa Expiry Date',
        'columnNameWithTable'=>'sa.student_indian_visa_expiry_date',
        'columnName'=>'student_indian_visa_expiry_date',
        'varName'=>'visadate',
        'table'=>'student_admission',
        'index'=>'71',
        'required'=>false,
        'displayType'=>'datetimepicker',
        'dataType'=>'date',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Identification Mark1',
        'columnNameWithTable'=>'sa.identification_mark1',
        'columnName'=>'identification_mark1',
        'varName'=>'identificationmark1',
        'table'=>'student_admission',
        'index'=>'72',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Identification Mark2',
        'columnNameWithTable'=>'sa.identification_mark2',
        'columnName'=>'identification_mark2',
        'varName'=>'identificationmark2',
        'table'=>'student_admission',
        'index'=>'73',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Sibling1 Name',
        'columnNameWithTable'=>'sa.sibling1_name',
        'columnName'=>'sibling1_name',
        'varName'=>'sibling1Name',
        'table'=>'student_admission',
        'index'=>'74',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Sibling1 Occupation',
        'columnNameWithTable'=>'sa.sibling1_occupation',
        'columnName'=>'sibling1_occupation',
        'varName'=>'sibling1Occupation',
        'table'=>'student_admission',
        'index'=>'75',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Sibling1 Mobile Number',
        'columnNameWithTable'=>'sa.sibling1_mobile_num',
        'columnName'=>'sibling1_mobile_num',
        'varName'=>'sibling1mobileNum',
        'table'=>'student_admission',
        'index'=>'76',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Sibling2 Name',
        'columnNameWithTable'=>'sa.sibling2_name',
        'columnName'=>'sibling2_name',
        'varName'=>'sibling2Name',
        'table'=>'student_admission',
        'index'=>'77',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Sibling2 Occupation',
        'columnNameWithTable'=>'sa.sibling2_occupation',
        'columnName'=>'sibling2_occupation',
        'varName'=>'sibling2Occupation',
        'table'=>'student_admission',
        'index'=>'78',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Sibling3 Name',
        'columnNameWithTable'=>'sa.sibling3_name',
        'columnName'=>'sibling3_name',
        'varName'=>'sibling3Name',
        'table'=>'student_admission',
        'index'=>'79',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Sibling3 Occupation',
        'columnNameWithTable'=>'sa.sibling3_occupation',
        'columnName'=>'sibling3_occupation',
        'varName'=>'sibling3Occupation',
        'table'=>'student_admission',
        'index'=>'80',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Sibling3 Mobile Number',
        'columnNameWithTable'=>'sa.sibling3_mobile_num',
        'columnName'=>'sibling3_mobile_num',
        'varName'=>'sibling3mobileNum',
        'table'=>'student_admission',
        'index'=>'81',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student Whatsapp Number',
        'columnNameWithTable'=>'sa.student_whatsapp_num',
        'columnName'=>'student_whatsapp_num',
        'varName'=>'studentwhatsappNum',
        'table'=>'student_admission',
        'index'=>'82',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Current Nearest Location',
        'columnNameWithTable'=>'sa.current_nearest_location',
        'columnName'=>'current_nearest_location',
        'varName'=>'currentNearLocation',
        'table'=>'student_admission',
        'index'=>'83',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'PassPort Number',
        'columnNameWithTable'=>'sa.passport_number',
        'columnName'=>'passport_number',
        'varName'=>'passportNumber',
        'table'=>'student_admission',
        'index'=>'84',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'PassPort Issued Place',
        'columnNameWithTable'=>'sa.passport_issued_place',
        'columnName'=>'passport_issued_place',
        'varName'=>'passportIssuedPlace',
        'table'=>'student_admission',
        'index'=>'85',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'PassPort Validity',
        'columnNameWithTable'=>'sa.passport_validity',
        'columnName'=>'passport_validity',
        'varName'=>'passportvalidity',
        'table'=>'student_admission',
        'index'=>'86',
        'required'=>false,
         'displayType'=>'datetimepicker',
        'dataType'=>'date',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Parents Marriage Anniversary',
        'columnNameWithTable'=>'sa.parents_marriage_anniversary',
        'columnName'=>'parents_marriage_anniversary',
        'varName'=>'marriageAnniversary',
        'table'=>'student_admission',
        'index'=>'87',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Father Bank Account Number',
        'columnNameWithTable'=>'f.bank_account_num',
        'columnName'=>'bank_account_num',
        'varName'=>'fbankAccountNumber',
        'table'=>'father',
        'index'=>'88',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Bank Account Number',
        'columnNameWithTable'=>'m.bank_account_num',
        'columnName'=>'bank_account_num',
        'varName'=>'mbankAccountNumber',
        'table'=>'mother',
        'index'=>'89',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Blood group',
        'columnNameWithTable'=>'f.blood_group',
        'columnName'=>'blood_group',
        'varName'=>'fbloodgroup',
        'table'=>'father',
        'index'=>'90',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Blood group',
        'columnNameWithTable'=>'m.blood_group',
        'columnName'=>'blood_group',
        'varName'=>'mbloodgroup',
        'table'=>'mother',
        'index'=>'91',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Date of Birth',
        'columnNameWithTable'=>'f.dob',
        'columnName'=>'dob',
        'varName'=>'fdob',
        'table'=>'father',
        'index'=>'92',
        'required'=>false,
        'displayType'=>'datetimepicker',
        'dataType'=>'date',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Date of Birth',
        'columnNameWithTable'=>'m.dob',
        'columnName'=>'dob',
        'varName'=>'mdob',
        'table'=>'mother',
        'index'=>'93',
        'required'=>false,
        'displayType'=>'datetimepicker',
        'dataType'=>'date',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Employee ID',
        'columnNameWithTable'=>'f.employee_id',
        'columnName'=>'employee_id',
        'varName'=>'fEmployeeId',
        'table'=>'father',
        'index'=>'94',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Employee ID',
        'columnNameWithTable'=>'m.employee_id',
        'columnName'=>'employee_id',
        'varName'=>'mEmployeeId',
        'table'=>'mother',
        'index'=>'95',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Office Landline Number',
        'columnNameWithTable'=>'m.office_landline_number',
        'columnName'=>'office_landline_number',
        'varName'=>'mofficeLandlineNum',
        'table'=>'mother',
        'index'=>'96',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Office Landline Number',
        'columnNameWithTable'=>'f.office_landline_number',
        'columnName'=>'office_landline_number',
        'varName'=>'fofficeLandlineNum',
        'table'=>'father',
        'index'=>'97',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Alternate Email Id',
        'columnNameWithTable'=>'f.alternate_email_id',
        'columnName'=>'alternate_email_id',
        'varName'=>'fAlternateEmail',
        'table'=>'father',
        'index'=>'98',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Father Whatsapp Number',
        'columnNameWithTable'=>'f.whatsapp_num',
        'columnName'=>'whatsapp_num',
        'varName'=>'fwhatsappNum',
        'table'=>'father',
        'index'=>'99',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Whatsapp Number',
        'columnNameWithTable'=>'m.whatsapp_num',
        'columnName'=>'whatsapp_num',
        'varName'=>'mwhatsappNum',
        'table'=>'mother',
        'index'=>'100',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Point of Contact',
        'columnNameWithTable'=>'sa.point_of_contact',
        'columnName'=>'point_of_contact',
        'varName'=>'pointOfcontact',
        'optionArrName'=>'pointOfContactOptions',
        'table'=>'student_admission',
        'index'=>'101',
        'required'=>false,
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student Living with',
        'columnNameWithTable'=>'sa.student_living_with',
        'columnName'=>'student_living_with',
        'varName'=>'studentLivingwith',
        'table'=>'student_admission',
        'index'=>'102',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Last TC Number',
        'columnNameWithTable'=>'sa.last_tc_num',
        'columnName'=>'last_tc_num',
        'varName'=>'lastTcnum',
        'table'=>'student_admission',
        'index'=>'103',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Last HallTicket Number',
        'columnNameWithTable'=>'sa.last_hallticket_num',
        'columnName'=>'last_hallticket_num',
        'varName'=>'lasthallticket',
        'table'=>'student_admission',
        'index'=>'104',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Identification Code',
        'columnNameWithTable' => 'sa.identification_code',
        'columnName'=>'identification_code',
        'varName'=>'identification_code',
        'table'=>'student_admission',
        'index'=>'105',
        'required'=>false,
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'School to home Distance',
        'columnNameWithTable'=>'sa.distance_from_school_to_home_in_km',
        'columnName'=>'distance_from_school_to_home_in_km',
        'varName'=>'distance_from_school_to_home_in_km',
        'table'=>'student_admission',
        'index'=>'106',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Father Caste',
        'columnNameWithTable'=>'f.caste',
        'columnName'=>'caste',
        'varName'=>'fcaste',
        'table'=>'father',
        'index'=>'107',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'Mother Caste',
        'columnNameWithTable'=>'m.caste',
        'columnName'=>'caste',
        'varName'=>'mcaste',
        'table'=>'mother',
        'index'=>'108',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'parent_info'
      ],
      [
        'displayName'=>'PEN Number',
        'columnNameWithTable'=>'sa.pen_number',
        'columnName'=>'pen_number',
        'varName'=>'Spen_number',
        'table'=>'student_admission',
        'index'=>'109',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Sub Caste',
        'columnNameWithTable'=>'sa.student_sub_caste',
        'columnName'=>'student_sub_caste',
        'varName'=>'Sstudent_sub_caste',
        'table'=>'student_admission',
        'index'=>'110',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Second Language Currently Studying',
        'columnNameWithTable'=>'sa.second_language_currently_studying',
        'columnName'=>'second_language_currently_studying',
        'optionArrName'=>'secondlangoptions',
        'varName'=>'second_language_currently_studying',
        'table'=>'student_admission',
        'index'=>'111',
        'displayType'=>'comboboxWithKeyValuePair',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Middle Name',
        'columnNameWithTable'=>'sa.student_middle_name',
        'columnName'=>'student_middle_name',
        'varName'=>'student_middle_name',
        'table'=>'student_admission',
        'index'=>'112',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ],
      [
        'displayName'=>'Student APPAR ID',
        'columnNameWithTable'=>'sa.apaar_id',
        'columnName'=>'apaar_id',
        'varName'=>'apaar_id',
        'table'=>'student_admission',
        'index'=>'113',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Caste Income Cerificate Number',
        'columnNameWithTable'=>'sa.caste_income_certificate_number',
        'columnName'=>'caste_income_certificate_number',
        'varName'=>'caste_income_certificate_number',
        'table'=>'student_admission',
        'index'=>'114',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'school_info'
      ],
      [
        'displayName'=>'Student Mobile Number',
        'columnNameWithTable'=>'sa.student_mobile_no',
        'columnName'=>'student_mobile_no',
        'varName'=>'student_mobile_no',
        'table'=>'student_admission',
        'index'=>'115',
        'displayType'=>'text',
        'dataType'=>'string',
        'info'=>'personal_info'
      ]
    ];

    //echo '<pre>';print_r($data['columnList']);die();
    $count = end($columnList)['index']+1;
    // $data['columnList_json'] = json_encode(array_merge($columnList,$this->__prepareAddressFields($columnList)));
    $selectedOptions = $this->Student_report_model->get_config_student_display_fileds();
    $selected_enabled_fields =(array) json_decode($selectedOptions[0]->value);
    $custom_fields = $this->settings->getSetting('student_admission_custom_fields');
    $custom_fields_student_year = $this->settings->getSetting('student_year_custom_fields');
    
    $data['columnList'] = $columnList;
    // echo '<pre>';print_r($custom_fields);die();
    $fields = [];
    foreach($data['columnList'] as $key=>$val){
      if(!empty($selected_enabled_fields[$val['info']])){
        if(in_array($val['columnName'],$selected_enabled_fields[$val['info']])){
          $fields[$key] = $val;
        }
      }
    }
    // $data['columnList'] = array_merge((array)$fields,$this->__prepareAddressFields($columnList));
    // $data['columnList'] = array_merge($data['columnList'],$this->__prepareCustomFields());
    $data['columnList'] = (array)$fields;
    if(!empty($custom_fields)){
      foreach ($custom_fields as $displayName => $columnName) {
        $custom_col = array(
          'displayName' => $displayName,
          'columnNameWithTable' => 'sa.'.$columnName,
          'columnName' => $columnName,
          'varName' => 's'.$columnName,
          'table' => 'student_admission',
          'index' => $count++,
          'displayType'=>'text',
          'dataType'=>'string',
          'info'=>'personal_info'
        );
        array_push($data['columnList'],$custom_col);
      }
    }

    if (!empty($custom_fields_student_year)) {
      foreach ($custom_fields_student_year as $displayName => $columnName) {
          $cleanColumnName = str_replace('sy_', '', $columnName);
          
          $custom_col = array(
              'displayName' => $displayName,
              'columnNameWithTable' => 'sy.' . $cleanColumnName,
              'columnName' => $cleanColumnName,
              'varName' => $cleanColumnName,
              'table' => 'student_year',
              'index' => $count++,
              'displayType' => 'text',
              'dataType' => 'string',
              'info' => 'personal_info'
          );
          array_push($data['columnList'], $custom_col);
      }
  }
    $data['columnList_json'] = json_encode($data['columnList']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_massupdate/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'student/student_massupdate/index_mobile';
    }else{
      $data['main_content'] = 'student/student_massupdate/index';    	
    }

    
    $this->load->view('inc/template', $data);
  }

  private function __prepareAddressFields($columnList) {
    $addresses = array('Student' => $this->settings->getSetting('student_address_types'));
    $addresses = array_merge($addresses, array('Father' => $this->settings->getSetting('father_address_types')));
    $addresses = array_merge($addresses, array('Mother' => $this->settings->getSetting('mother_address_types')));

    $addArr = array();
    $index = count($columnList) + 100;
    // echo '<pre>';print_r($index + 100);die();
    foreach ($addresses as $addKey => $sh) {
      if (empty($sh)) continue;
      foreach ($sh as $shKey => $add) {
        $obj = array();
        $obj['displayName'] = $addKey . '-' . $add;
        $obj['columnNameWithTable'] = 'ad.unformatted_address';
        $obj['varName'] = $addKey . '_' . $shKey . '_' . 'address';
        $obj['columnName'] = $addKey .  '_' . 'address_'.$shKey;
        $obj['table'] = 'address_info';
        $obj['index'] = ++$index;
        $obj['displayType'] = 'text';
        $obj['dataType'] = 'string';
        $obj['addressType'] = $shKey;

        // $objUn = array();
        // $objUn['displayName'] = $addKey . '-' . $add.'-Unformatted';
        // $objUn['varName'] = $addKey . '_' . $shKey . '_' . 'unaddress';
        // $objUn['table'] = 'address_info';
        // $objUn['index'] = ++$index;
        // $objUn['displayType'] = 'text';
        // $objUn['dataType'] = 'string';
        // $objUn['addressType'] = $shKey;
        switch ($addKey) {
          case 'Student':
          $obj['addressOf'] = 'Student';
          // $objUn['addressOf'] = 'Student';
            break;
          case 'Father':
          $obj['addressOf'] = 'Father';
          // $objUn['addressOf'] = 'Father';
            break;
          case 'Mother':
            $obj['addressOf'] = 'Mother';
            // $objUn['addressOf'] = 'Mother';
            break;
        }
        $addArr[] = $obj;
        // $addArr[] = $objUn;
      }
    }
    return $addArr;
    //  echo '<pre>';print_r($addArr);die();

  }

  private function __prepareCustomFields(){
    $custom_fields =$this->settings->getSetting('student_admission_custom_fields');
    $customFIelds = array();
    if($custom_fields){
      $indexNumber = 200;
      foreach ($custom_fields as $displayName => $columnName) {
        $obj = array();
        $obj['displayName'] = $displayName;
        $obj['columnNameWithTable'] = 'sa.'.$columnName;
        $obj['varName'] = $columnName;
        $obj['columnName'] = $columnName;
        $obj['table'] = 'student_admission';
        $obj['index'] = $indexNumber++;
        $obj['displayType'] = 'text';
        $obj['dataType'] = 'string';
        $customFIelds[] = $obj;
      }  
    }
    
    return $customFIelds;
  }

  public function startMassUpdate() {
    $mandatoryFields = [
      [
        'displayName'=>'Id',
        'columnNameWithTable'=>'sa.id',
        'columnName'=>'id',
        'varName'=>'saId',
        'table'=>'student_admission',
        'required'=>false,'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Student Id',
        'columnNameWithTable'=>'sa.id',
        'columnName'=>'id',
        'varName'=>'stu_id',
        'table'=>'student_admission',
        'required'=>false,'displayType'=>'label',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Id',
        'columnNameWithTable'=>'sy.id',
        'columnName'=>'id',
        'varName'=>'syId',
        'table'=>'student_year',
        'required'=>false,'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'User Id',
        'columnNameWithTable'=>'us.id',
        'columnName'=>'id',
        'varName'=>'usId',
        'table'=>'users',
        'required'=>false,'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Father User Id',
        'columnNameWithTable'=>'fus.id',
        'columnName'=>'id',
        'varName'=>'fusId',
        'table'=>'father_users',
        'required'=>false,'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Mother User Id',
        'columnNameWithTable'=>'mus.id',
        'columnName'=>'id',
        'varName'=>'musId',
        'table'=>'mother_users',
        'required'=>false,'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Father Id',
        'columnNameWithTable'=>'f.id',
        'columnName'=>'id',
        'varName'=>'fId',
        'table'=>'father',
        'required'=>false,'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Mother Id',
        'columnNameWithTable'=>'m.id',
        'columnName'=>'id',
        'varName'=>'mId',
        'table'=>'mother',
        'required'=>false,'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Roll number',
        'columnNameWithTable'=>'sy.roll_no',
        'columnName'=>'roll_no',
        'varName'=>'sRollNo',
        'table'=>'student_year',
        'required'=>false,'displayType'=>'label',
        'dataType'=>'string'
      ],
      // [
      //   'displayName'=>'Admission Number',
      //   'columnNameWithTable'=>'s.admission_no',
      //   'columnName'=>'admission_no',
      //   'varName'=>'sAdmissionNo',
      //   'table'=>'student',
      //   'required'=>false,'displayType'=>'label',
      //   'dataType'=>'string'
      // ],
      [
        'displayName'=>'Section',
        'columnNameWithTable'=>'cs.section_name',
        'columnName'=>'section_name',
        'varName'=>'csSectionName',
        'table'=>'class_section',
        'required'=>false,'displayType'=>'label',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'First Name',
        'columnNameWithTable'=>'sa.first_name',
        'columnName'=>'first_name',
        'varName'=>'sFirstName',
        'table'=>'student_admission',
        'required'=>false,'displayType'=>'label',
        'dataType'=>'string'
      ]
    ];
    $selectedIndex = $this->input->post('fields');
    $columnList = json_decode($this->input->post('columnList_json'));
    $sectionId = $this->input->post('sectionId');
    $classId = $this->input->post('classId');
    $admission_status = $this->input->post('admission_status');
    // $letters = $this->input->post('letters');

    // echo '<pre>';print_r($_POST);die();

    //Get the selected Columns
    $selectedColumns = array();
    $data['admission_numbers'] = array();
    foreach($selectedIndex as $fIndex) {
      $found = 0;
      foreach ($columnList as $col) {
        if ($col->index == $fIndex) {
          $found = 1;
          $selectedColumns[] = (array)$col;
          if($col->columnName == 'admission_no') {
            $data['admission_numbers'] = $this->Student_massupdate_model->getAdmissionNos();
          }
          break;
        }
      }
    }

    $allColumns = array_merge($mandatoryFields, $selectedColumns);
    //Get column string
    $colString = '';
    foreach ($allColumns as $col) {
      // if ($sectionId == '9999' && $col['columnNameWithTable'] == 'cs.section_name') {
      //   //Do nothing
      // } else {
      //   if ($colString != '') {
      //     $colString .= ',';
      //   }
      //   $colString .= $col['columnNameWithTable'] . ' as ' . $col['varName'];
      // }
      if ($colString != '') {
            $colString .= ',';
          }
          $colString .= $col['columnNameWithTable'] . ' as ' . $col['varName'];
    }

    //echo '<pre>';print_r($colString);die();

    $optionObjects = new stdClass();
    $optionObjects->nationalityOptions = $this->config->item('nationality');
    $optionObjects->religionOptions = $this->config->item('religions');
    $optionObjects->motherTongueOptions = $this->config->item('languages');
    $optionObjects->mediumOptions = $this->Student_massupdate_model->getMediumOptions();
    $optionObjects->boardOptions = $this->Student_massupdate_model->getBoardOptions();
    $optionObjects->boardingOptions = $this->Student_massupdate_model->getBoardingOptions();

    $optionObjects->admissionType = $this->Student_massupdate_model->getAdmissionTypeOptions();
    $optionObjects->is_rte = $this->Student_massupdate_model->getRTEOptions();
    $optionObjects->category = $this->Student_massupdate_model->getCategoryOptions();
    $optionObjects->quota = $this->Student_massupdate_model->getQuotaOptions();
    $optionObjects->attempt = $this->Student_massupdate_model->getAttemptOptions();
    $optionObjects->subLangOptions = $this->config->item('sub_language');
    $optionObjects->assOptions = $this->config->item('after_school_sports_options');
    $optionObjects->blood_group = $this->config->item('blood_groups');
    $optionObjects->casteOptions = $this->Student_massupdate_model->getStaffDistinctColumn('caste','student_admission');
    $optionObjects->houseOptions = $this->Student_massupdate_model->getStaffDistinctColumn('student_house','student_year');
    $optionObjects->csOptions = $this->Student_massupdate_model->getSectionOptions($classId);

    $optionObjects->combOption = $this->Student_massupdate_model->getComboOptions($classId);
    $optionObjects->combinationOption = $this->Student_massupdate_model->getCombList_by_class_id($classId);
    $optionObjects->semseterList = $this->Student_massupdate_model->getSemesterOptions();



    $optionObjects->classOptions = $this->Student_massupdate_model->getClassOptions();
    $optionObjects->sClassAdmitted = $this->Student_massupdate_model->getClassAdmittedOptions();

    $optionObjects->genderOptions = $this->Student_massupdate_model->getStaffDistinctColumn('gender','student_admission');
    $optionObjects->secondlangoptions = [(Object)['name'=>'Kannada', 'value'=>'Kannada'],(Object)['name'=>'Telugu', 'value'=>'Telugu'],(Object)['name'=>'English', 'value'=>'English'],(Object)['name'=>'Hindi', 'value'=>'Hindi'],(Object)['name'=>'German', 'value'=>'German'],(Object)['name'=>'French', 'value'=>'French'],(Object)['name'=>'Spanish', 'value'=>'Spanish'],(Object)['name'=>'Sanskrit', 'value'=>'Sanskrit']];
    $optionObjects->acadyearOptions = $this->Student_massupdate_model->getAcadYearOptions();
    $optionObjects->preferredParentOptions = [(Object)['name'=>'Father', 'value'=>'Father'],(Object)['name'=>'Mother', 'value'=>'Mother'],(Object)['name'=>'Both', 'value'=>'Both']];
    $optionObjects->pointOfContactOptions = [(Object)['name'=>'Father', 'value'=>'Father'],(Object)['name'=>'Mother', 'value'=>'Mother'],(Object)['name'=>'Gaurdian', 'value'=>'Gaurdian']];
    // echo '<pre>'; print_r($optionObjects->preferredParentOptions); die();
    // echo '<pre>';print_r($optionObjects->admission_type);
    // echo '<pre>';print_r($optionObjects->is_rte);

    // die();


    $data['field_objects'] = $allColumns;
    $data['optionObjects'] = $optionObjects;
    $data['columnList_json'] = json_encode($allColumns);
    // $data['r_field_names'] = $r_field_names;
    $data['className'] = $this->Student_massupdate_model->getClassName($classId);
    $data['sectionName'] = $this->Student_massupdate_model->getSectionName($sectionId);
    $data['exportData'] = $this->Student_massupdate_model->getStudentData($colString,$classId,$sectionId,$admission_status);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'student/student_massupdate/assign_values_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'student/student_massupdate/assign_values_mobile';
    }else{
      $data['main_content']    = 'student/student_massupdate/assign_values';      	
    }
    $this->load->view('inc/template', $data);
  }

  public function mass_update_height_weight(){
    $data['classList'] = $this->Student_Model->getclass();
    $data['main_content']    = 'student/student_massupdate/update_hight_weight'; 
    $this->load->view('inc/template', $data);
  }

  public function address_mass_update(){
    $student_address_types = $this->settings->getSetting('student_address_types');
    $father_address_types = $this->settings->getSetting('father_address_types');
    $mother_address_types = $this->settings->getSetting('mother_address_types');
    $data['address_types'] = array();
    if(!empty($student_address_types)){
      foreach($student_address_types as $key => $val){
        $data['address_types']['student_'.$key] = 'Student '.$val;
      }
    }
    if(!empty($father_address_types)){
      foreach($father_address_types as $key => $val){
        $data['address_types']['Father_'.$key] = 'Father '.$val;
      }
    }
    if(!empty($mother_address_types)){
      foreach($mother_address_types as $key => $val){
        $data['address_types']['Mother_'.$key] = 'Mother '.$val;
      }
    }
    $data['classList'] = $this->Student_Model->getclass();
    $data['main_content']    = 'student/student_massupdate/address_mass_update'; 
    $this->load->view('inc/template', $data);
  }
  public function submitMassUpdate () {
    $this->Student_massupdate_model->store_edit_history();
    $status = $this->Student_massupdate_model->submitMassUpdate();
    switch ($status) {
      case 0:
        $this->session->set_flashdata('flashError', 'No data was updated OR Something went wrong');
        break;
      case -10:
        $this->session->set_flashdata('flashError', 'Unique check failed');
        break;
      default:
        $this->session->set_flashdata('flashSuccess', 'Data Updated Successfully.');
        break;
    }
    redirect('student/student_massupdate');
  }

  public function getSelectedStudentCount() {
    $letters = $_POST['letters'];
    $class_id = $_POST['class_id'];
    $section_id = $_POST['section_id'];
    echo $this->Student_massupdate_model->getSelectedStudentCount($letters, $class_id, $section_id);
  }

  public function get_class_section_names(){
    $result = $this->Student_massupdate_model->get_class_names();
    echo json_encode($result);
  }

  public function get_student_names_section_wise(){
    if($_POST['type'] == 'Insert'){
      $result = $this->Student_massupdate_model->get_student_names_section_wise($_POST);
    }else{
      $result = $this->Student_massupdate_model->get_student_height_weight_data($_POST);
    }
    echo json_encode($result);
  }

  public function submit_student_height_weight(){
    $result = $this->Student_massupdate_model->submit_student_height_weight($_POST);
    echo $result;
  }

  public function upload_students_data_csv(){
    $file_path = $_FILES['upload_csv']['tmp_name'];

    $csv_data_arr = [];
    $this->load->library('csvimport');
    if ($this->csvimport->get_array($file_path)) {
      $csv_data_arr = $this->csvimport->get_array($file_path);
    }
    $csv_data=[];
    foreach($csv_data_arr as $key => $value) {
      $csv_data[$value["Student Id"]]["student_id"] = $value["Student Id"];
      $csv_data[$value["Student Id"]]['data'][] = $value;
    }
    echo json_encode($csv_data);
  }

  public function get_student_list(){
    $relation_type = explode('_',$_POST['address_type'])[0];
    $address_type = explode('_',$_POST['address_type'])[1];
    if($relation_type == 'student'){
      $result = $this->Student_massupdate_model->get_student_address_data($_POST['class_id'],$address_type);
    }else{
      $result = $this->Student_massupdate_model->get_parent_address_data($_POST['class_id'],$address_type,$relation_type);
    }
    echo json_encode($result);
  }

  public function submit_address_MassUpdate(){
    $relation = explode('_',$_POST['address_type'])[0];
    $address_type = explode('_',$_POST['address_type'])[1];
    $this->Student_massupdate_model->store_mass_address_history();
    if($relation == 'student'){
      echo $this->Student_massupdate_model->student_mass_address_update($_POST,$address_type);
    }else{
      echo $this->Student_massupdate_model->parent_mass_address_update($_POST,$address_type);
    }
  }

  public function mass_insert_height_weight(){
    echo $this->Student_massupdate_model->mass_insert_height_weight($_POST);
  }
}

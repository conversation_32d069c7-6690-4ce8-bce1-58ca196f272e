<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Staff Login Helper
 * 
 * Helper functions for staff login logging functionality
 * 
 * @package    CodeIgniter
 * @subpackage Helpers
 * @category   Authentication
 * <AUTHOR> Name
 * @version    1.0
 */

if (!function_exists('get_current_user_login_info')) {
    /**
     * Get current user's login information
     * 
     * @return array|null Login information or null if not logged in
     */
    function get_current_user_login_info() {
        $CI =& get_instance();
        
        if (!$CI->ion_auth->logged_in()) {
            return null;
        }
        
        $login_log_id = $CI->session->userdata('staff_login_log_id');
        
        if (!$login_log_id) {
            return null;
        }
        
        $login_info = $CI->db->where('id', $login_log_id)
                            ->where('is_active', 1)
                            ->get('staff_login_logs')
                            ->row_array();
        
        return $login_info;
    }
}

if (!function_exists('get_user_active_sessions')) {
    /**
     * Get all active sessions for a user
     * 
     * @param int $user_id User ID (optional, uses current user if not provided)
     * @return array Active sessions
     */
    function get_user_active_sessions($user_id = null) {
        $CI =& get_instance();
        
        if (!$user_id) {
            if (!$CI->ion_auth->logged_in()) {
                return array();
            }
            $user = $CI->ion_auth->user()->row();
            $user_id = $user->id;
        }
        
        return $CI->db->where('user_id', $user_id)
                     ->where('is_active', 1)
                     ->order_by('login_time', 'DESC')
                     ->get('staff_login_logs')
                     ->result_array();
    }
}

if (!function_exists('get_user_login_history')) {
    /**
     * Get login history for a user
     * 
     * @param int $user_id User ID (optional, uses current user if not provided)
     * @param int $limit Number of records to return
     * @return array Login history
     */
    function get_user_login_history($user_id = null, $limit = 20) {
        $CI =& get_instance();
        
        if (!$user_id) {
            if (!$CI->ion_auth->logged_in()) {
                return array();
            }
            $user = $CI->ion_auth->user()->row();
            $user_id = $user->id;
        }
        
        return $CI->db->select('id, username, ip_address, login_time, logout_time, session_duration, device_type, browser_name, operating_system, logout_reason, is_active')
                     ->where('user_id', $user_id)
                     ->order_by('login_time', 'DESC')
                     ->limit($limit)
                     ->get('staff_login_logs')
                     ->result_array();
    }
}

if (!function_exists('force_user_logout')) {
    /**
     * Force logout a user by setting a session flag
     * 
     * @param int $user_id User ID
     * @param string $reason Reason for forced logout
     * @return bool Success status
     */
    function force_user_logout($user_id, $reason = 'admin_action') {
        $CI =& get_instance();
        
        // Close all active sessions for the user
        $CI->load->library('staff_login_logger');
        $CI->staff_login_logger->close_user_active_sessions($user_id, 'forced');
        
        // If the user is currently logged in, set a flag for immediate logout
        $current_user = $CI->ion_auth->user()->row();
        if ($current_user && $current_user->id == $user_id) {
            $CI->session->set_userdata('force_logout', TRUE);
        }
        
        return TRUE;
    }
}

if (!function_exists('get_login_statistics')) {
    /**
     * Get login statistics for reporting
     * 
     * @param int $days Number of days to include in statistics
     * @return array Login statistics
     */
    function get_login_statistics($days = 30) {
        $CI =& get_instance();
        
        $start_date = date('Y-m-d', strtotime("-{$days} days"));
        
        // Total logins
        $total_logins = $CI->db->where('login_time >=', $start_date)
                              ->count_all_results('staff_login_logs');
        
        // Unique users
        $unique_users = $CI->db->select('user_id')
                              ->where('login_time >=', $start_date)
                              ->group_by('user_id')
                              ->count_all_results('staff_login_logs');
        
        // Unique IPs
        $unique_ips = $CI->db->select('ip_address')
                            ->where('login_time >=', $start_date)
                            ->group_by('ip_address')
                            ->count_all_results('staff_login_logs');
        
        // Average session duration
        $avg_duration = $CI->db->select('AVG(session_duration) as avg_duration')
                              ->where('login_time >=', $start_date)
                              ->where('session_duration IS NOT NULL')
                              ->get('staff_login_logs')
                              ->row();
        
        // Daily login counts
        $daily_logins = $CI->db->select('DATE(login_time) as login_date, COUNT(*) as login_count')
                              ->where('login_time >=', $start_date)
                              ->group_by('DATE(login_time)')
                              ->order_by('login_date', 'DESC')
                              ->get('staff_login_logs')
                              ->result_array();
        
        // Browser statistics
        $browser_stats = $CI->db->select('browser_name, COUNT(*) as count')
                               ->where('login_time >=', $start_date)
                               ->where('browser_name IS NOT NULL')
                               ->group_by('browser_name')
                               ->order_by('count', 'DESC')
                               ->get('staff_login_logs')
                               ->result_array();
        
        // Device type statistics
        $device_stats = $CI->db->select('device_type, COUNT(*) as count')
                              ->where('login_time >=', $start_date)
                              ->where('device_type IS NOT NULL')
                              ->group_by('device_type')
                              ->order_by('count', 'DESC')
                              ->get('staff_login_logs')
                              ->result_array();
        
        return array(
            'total_logins' => $total_logins,
            'unique_users' => $unique_users,
            'unique_ips' => $unique_ips,
            'avg_session_duration' => $avg_duration ? round($avg_duration->avg_duration / 60, 2) : 0, // in minutes
            'daily_logins' => $daily_logins,
            'browser_stats' => $browser_stats,
            'device_stats' => $device_stats,
            'period_days' => $days
        );
    }
}

if (!function_exists('is_suspicious_login')) {
    /**
     * Check if a login attempt is suspicious based on various factors
     * 
     * @param int $user_id User ID
     * @param string $ip_address IP address
     * @return array Suspicion analysis
     */
    function is_suspicious_login($user_id, $ip_address) {
        $CI =& get_instance();
        
        $suspicion_factors = array();
        $suspicion_score = 0;
        
        // Check if IP is new for this user
        $ip_history = $CI->db->where('user_id', $user_id)
                            ->where('ip_address', $ip_address)
                            ->count_all_results('staff_login_logs');
        
        if ($ip_history == 0) {
            $suspicion_factors[] = 'New IP address for user';
            $suspicion_score += 30;
        }
        
        // Check for multiple recent login attempts from different IPs
        $recent_ips = $CI->db->select('DISTINCT ip_address')
                            ->where('user_id', $user_id)
                            ->where('login_time >=', date('Y-m-d H:i:s', strtotime('-1 hour')))
                            ->get('staff_login_logs')
                            ->num_rows();
        
        if ($recent_ips > 3) {
            $suspicion_factors[] = 'Multiple IPs in short time period';
            $suspicion_score += 40;
        }
        
        // Check for rapid login attempts
        $recent_logins = $CI->db->where('user_id', $user_id)
                               ->where('login_time >=', date('Y-m-d H:i:s', strtotime('-10 minutes')))
                               ->count_all_results('staff_login_logs');
        
        if ($recent_logins > 5) {
            $suspicion_factors[] = 'Rapid login attempts';
            $suspicion_score += 25;
        }
        
        // Check for unusual time (outside normal working hours)
        $hour = (int)date('H');
        if ($hour < 6 || $hour > 22) {
            $suspicion_factors[] = 'Login outside normal hours';
            $suspicion_score += 15;
        }
        
        // Determine suspicion level
        $suspicion_level = 'low';
        if ($suspicion_score >= 50) {
            $suspicion_level = 'high';
        } elseif ($suspicion_score >= 25) {
            $suspicion_level = 'medium';
        }
        
        return array(
            'is_suspicious' => $suspicion_score > 0,
            'suspicion_level' => $suspicion_level,
            'suspicion_score' => $suspicion_score,
            'factors' => $suspicion_factors
        );
    }
}

if (!function_exists('log_manual_staff_login')) {
    /**
     * Manually log a staff login (for special cases)
     * 
     * @param int $user_id User ID
     * @param string $username Username
     * @param string $login_method Login method
     * @param array $additional_data Additional data to log
     * @return int|bool Login log ID on success, FALSE on failure
     */
    function log_manual_staff_login($user_id, $username, $login_method = 'manual', $additional_data = array()) {
        $CI =& get_instance();
        $CI->load->library('staff_login_logger');
        
        return $CI->staff_login_logger->log_login($user_id, $username, $login_method);
    }
}

if (!function_exists('update_session_ip')) {
    /**
     * Manually update the current session's IP address
     * Useful for tracking IP changes during an active session
     * 
     * @return bool Success status
     */
    function update_session_ip() {
        $CI =& get_instance();
        $CI->load->library('staff_login_logger');
        
        return $CI->staff_login_logger->track_session_ip();
    }
}

if (!function_exists('get_session_duration')) {
    /**
     * Get the current session duration in minutes
     * 
     * @return int|null Session duration in minutes or null if not available
     */
    function get_session_duration() {
        $login_info = get_current_user_login_info();
        
        if (!$login_info) {
            return null;
        }
        
        $login_time = strtotime($login_info['login_time']);
        $current_time = time();
        
        return round(($current_time - $login_time) / 60);
    }
}

if (!function_exists('format_session_duration')) {
    /**
     * Format session duration in a human-readable format
     *
     * @param int $seconds Duration in seconds
     * @return string Formatted duration
     */
    function format_session_duration($seconds) {
        if (!$seconds) {
            return 'N/A';
        }

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        $parts = array();

        if ($hours > 0) {
            $parts[] = $hours . 'h';
        }

        if ($minutes > 0) {
            $parts[] = $minutes . 'm';
        }

        if ($seconds > 0 && count($parts) < 2) {
            $parts[] = $seconds . 's';
        }

        return implode(' ', $parts);
    }
}

if (!function_exists('get_staff_login_setting')) {
    /**
     * Safely get a staff login setting value
     *
     * @param string $setting_name Setting name
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    function get_staff_login_setting($setting_name, $default = null) {
        $CI =& get_instance();

        // Check if settings library is available
        if (isset($CI->settings)) {
            return $CI->settings->getSetting($setting_name);
        }

        // Fallback: check database directly
        $result = $CI->db->select('value')
                        ->where('name', $setting_name)
                        ->get('settings')
                        ->row();

        return $result ? $result->value : $default;
    }
}

<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Staff Login Hook
 *
 * This hook automatically captures staff login and logout events
 * without modifying existing authentication code.
 *
 * @package    CodeIgniter
 * @subpackage Hooks
 * @category   Authentication
 * <AUTHOR> Name
 * @version    1.0
 */

/**
 * Helper function to safely get controller and method information
 *
 * @return array Controller and method information
 */
function get_controller_info() {
    $CI =& get_instance();

    $controller = null;
    $method = null;
    $directory = null;

    // Try to get from router first
    if ($CI && isset($CI->router)) {
        try {
            $controller = $CI->router->fetch_class();
            $method = $CI->router->fetch_method();
            $directory = $CI->router->fetch_directory();
        } catch (Exception $e) {
            // Router not available, try alternative methods
        }
    }

    // Fallback: try to get from URI segments
    if (!$controller && $CI && isset($CI->uri)) {
        try {
            $segments = $CI->uri->segment_array();
            if (!empty($segments)) {
                $controller = isset($segments[1]) ? $segments[1] : null;
                $method = isset($segments[2]) ? $segments[2] : 'index';
            }
        } catch (Exception $e) {
            // URI not available either
        }
    }
    // Final fallback: check $_SERVER variables
    if (!$controller) {
        $request_uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        if (strpos($request_uri, '/auth/') !== false) {
            $controller = 'auth';
            if (strpos($request_uri, '/login') !== false) {
                $method = 'login';
            } elseif (strpos($request_uri, '/logout') !== false) {
                $method = 'logout';
            }
        }
    }
    return array(
        'controller' => $controller,
        'method' => $method,
        'directory' => $directory
    );
}

/**
 * Monitor login events
 * This function is called after controller constructor to check for login events
 */
function monitor_staff_login() {
    $CI =& get_instance();

    // Check if CI instance is available
    if (!$CI) {
        return;
    }

    // Get controller information safely
    $info = get_controller_info();
    
    $controller = $info['controller'];
    $method = $info['method'];
    // Skip if we can't determine controller/method or not authentication related
    if (!$controller || !$method ||
        !in_array(strtolower($controller), ['auth']) ||
        !in_array(strtolower($method), ['login', 'logout'])) {
        return;
    }

    // Load the staff login logger library
    $CI->load->library('staff_login_logger');

    // Check if this is a login attempt
    if (strtolower($method) === 'login' && $CI->input->post()) {
        // Ensure session is available before using it
        if (isset($CI->session)) {
            // Store login attempt data in session for post-processing
            $CI->session->set_tempdata('login_attempt_data', array(
                'identity' => $CI->input->post('identity'),
                'timestamp' => time(),
                'ip_address' => $CI->input->ip_address(),
                'user_agent' => $CI->input->user_agent()
            ), 300); // Keep for 5 minutes
        }
    }
}

/**
 * Log successful login after authentication
 * This function is called after the controller method execution
 */
function log_successful_login() {
    $CI =& get_instance();

    // Check if CI instance is available
    if (!$CI) {
        return;
    }

    // Get controller information safely
    $info = get_controller_info();
    $controller = $info['controller'];
    $method = $info['method'];

    // Only proceed if we're in the Auth controller
    if (!$controller || !$method ||
        strtolower($controller) !== 'auth' ||
        strtolower($method) !== 'login') {
        return;
    }

    // Check if user is now logged in (successful login)
    if (isset($CI->ion_auth) && $CI->ion_auth->logged_in()) {
        $CI->load->library('staff_login_logger');

        $user = $CI->ion_auth->user()->row();
        $login_attempt_data = null;

        // Safely get session data
        if (isset($CI->session)) {
            $login_attempt_data = $CI->session->tempdata('login_attempt_data');
        }

        if ($user && $login_attempt_data) {
            // Log the successful login
            $login_log_id = $CI->staff_login_logger->log_login(
                $user->id,
                $user->username,
                'web'
            );

            if ($login_log_id && isset($CI->session)) {
                // Clear the temporary login attempt data
                $CI->session->unset_tempdata('login_attempt_data');

                // Set a flag to indicate successful login logging
                $CI->session->set_userdata('login_logged', TRUE);
            }
        }
    }
}

/**
 * Log logout events
 * This function is called before the logout method execution
 */
function log_logout_event() {
    $CI =& get_instance();

    // Check if CI instance is available
    if (!$CI) {
        return;
    }

    // Get controller information safely
    $info = get_controller_info();
    $controller = $info['controller'];
    $method = $info['method'];

    // Only proceed if we're in the Auth controller logout method
    if (!$controller || !$method ||
        strtolower($controller) !== 'auth' ||
        strtolower($method) !== 'logout') {
        return;
    }

    // Check if user is currently logged in
    if (isset($CI->ion_auth) && $CI->ion_auth->logged_in()) {
        $CI->load->library('staff_login_logger');

        $user = $CI->ion_auth->user()->row();
        if ($user) {
            // Log the logout event
            $CI->staff_login_logger->log_logout($user->id, 'manual');
        }
    }
}

/**
 * Track session-based IP changes for active users
 * This function can be called periodically to track IP changes
 */
function track_session_activity() {
    $CI =& get_instance();

    // Check if CI instance is available
    if (!$CI) {
        return;
    }

    // Get controller information safely
    $info = get_controller_info();
    $controller = $info['controller'];

    // Skip for login/logout pages and AJAX requests
    if ($controller && strtolower($controller) === 'auth') {
        return;
    }

    if ($CI->input->is_ajax_request() || !isset($CI->ion_auth) || !$CI->ion_auth->logged_in()) {
        return;
    }

    // Ensure session is available
    if (!isset($CI->session)) {
        return;
    }

    // Only track every few minutes to avoid performance impact
    $last_track_time = $CI->session->userdata('last_ip_track_time');
    $current_time = time();

    // Track every 5 minutes
    if (!$last_track_time || ($current_time - $last_track_time) > 300) {
        $CI->load->library('staff_login_logger');
        $CI->staff_login_logger->track_session_ip();
        $CI->session->set_userdata('last_ip_track_time', $current_time);
    }
}

/**
 * Auto-logout inactive sessions
 * This function checks for session timeout and logs out inactive users
 */
function check_session_timeout() {
    $CI =& get_instance();

    // Check if CI instance is available
    if (!$CI) {
        return;
    }

    // Get controller information safely
    $info = get_controller_info();
    $controller = $info['controller'];

    // Skip for login pages and AJAX requests
    if ($controller && strtolower($controller) === 'auth') {
        return;
    }

    if ($CI->input->is_ajax_request()) {
        return;
    }
    
    // Check if user is logged in
    if (isset($CI->ion_auth) && $CI->ion_auth->logged_in()) {
        $CI->load->library('staff_login_logger');

        // Get session timeout from config (default 24 hours)
        $timeout_minutes = 1440; // 24 hours

        // Ensure session is available
        if (!isset($CI->session)) {
            return;
        }

        // Check if we have a login log ID in session
        $login_log_id = $CI->session->userdata('staff_login_log_id');
        
        if ($login_log_id) {
            // Get the login record
            $login_record = $CI->db->where('id', $login_log_id)
                                  ->where('is_active', 1)
                                  ->get('staff_login_logs')
                                  ->row();
            
            if ($login_record) {
                $login_time = strtotime($login_record->login_time);
                $current_time = time();
                $session_minutes = ($current_time - $login_time) / 60;
                
                // If session has exceeded timeout, log out the user
                if ($session_minutes > $timeout_minutes) {
                    $user = $CI->ion_auth->user()->row();
                    if ($user) {
                        // Log the timeout logout
                        $CI->staff_login_logger->log_logout($user->id, 'timeout');
                        
                        // Perform actual logout
                        $CI->ion_auth->logout();
                        $CI->session->sess_destroy();
                        
                        // Redirect to login with timeout message
                        if (isset($CI->session)) {
                            $CI->session->set_flashdata('flashError', 'Your session has expired due to inactivity. Please login again.');
                        }
                        redirect('auth/login', 'refresh');
                    }
                }
            }
        }
    }
}

/**
 * Monitor API login events
 * This function handles API-based login events
 */
function monitor_api_login() {
    $CI =& get_instance();

    // Check if CI instance is available
    if (!$CI) {
        return;
    }

    // Get controller information safely
    $info = get_controller_info();
    $controller = $info['controller'];
    $directory = $info['directory'];
    $method = $info['method'];

    // Check if this is an API authentication request
    if ($directory === 'apiv2/' &&
        $controller && strtolower($controller) === 'auth' &&
        $method && strtolower($method) === 'login_post') {
        // Ensure session is available
        if (isset($CI->session)) {
            // This will be handled in the post_controller hook for API responses
            $CI->session->set_tempdata('api_login_attempt', array(
                'timestamp' => time(),
                'ip_address' => $CI->input->ip_address(),
                'user_agent' => $CI->input->user_agent(),
                'username' => $CI->input->post('username')
            ), 300);
        }
    }
}

/**
 * Log API login success
 * This function logs successful API login events
 */
function log_api_login_success() {
    $CI =& get_instance();

    // Check if CI instance is available
    if (!$CI || !isset($CI->session)) {
        return;
    }

    // Check if this was an API login attempt
    $api_attempt = $CI->session->tempdata('api_login_attempt');

    if ($api_attempt && isset($CI->ion_auth) && $CI->ion_auth->logged_in()) {
        $CI->load->library('staff_login_logger');

        $user = $CI->ion_auth->user()->row();
        if ($user) {
            // Log the successful API login
            $CI->staff_login_logger->log_login(
                $user->id,
                $user->username,
                'api'
            );

            // Clear the temporary data
            $CI->session->unset_tempdata('api_login_attempt');
        }
    }
}

/**
 * Cleanup old login logs periodically
 * This function runs cleanup based on configuration
 */
function cleanup_login_logs() {
    $CI =& get_instance();
    
    // Only run cleanup occasionally (e.g., 1% chance on each request)
    if (rand(1, 100) === 1) {
        $CI->load->library('staff_login_logger');
        $CI->staff_login_logger->cleanup_old_logs();
    }
}

/**
 * Handle forced logout scenarios
 * This function can be used to force logout users in specific scenarios
 */
function handle_forced_logout() {
    $CI =& get_instance();

    // Check if CI instance and session are available
    if (!$CI || !isset($CI->session)) {
        return;
    }

    // Check for forced logout flag (can be set by admin or system)
    $forced_logout = $CI->session->userdata('force_logout');

    if ($forced_logout && isset($CI->ion_auth) && $CI->ion_auth->logged_in()) {
        $CI->load->library('staff_login_logger');

        $user = $CI->ion_auth->user()->row();
        if ($user) {
            // Log the forced logout
            $CI->staff_login_logger->log_logout($user->id, 'forced');

            // Perform logout
            $CI->ion_auth->logout();
            $CI->session->sess_destroy();

            // Redirect with message
            $CI->session->set_flashdata('flashError', 'You have been logged out by the system administrator.');
            redirect('auth/login', 'refresh');
        }
    }
}

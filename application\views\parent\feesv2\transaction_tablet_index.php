<div class="card" style="box-shadow: none;border: none;margin-bottom:20%;margin-top:9%">
  <div class="card-header panel_heading_new_style text-center">
    <span class="card-title panel_title_new_style"><strong>Transaction Summary</strong></span>
  </div>
   
    <div class="card-body">
      <?php 
      $acadyearStds = [];
        foreach ($acad_year_selection as $key => $val) {
          if (!in_array($val->acad_year_id, $acadyearStds)) {
            $acadyearStds[$val->acad_year_id] = $val->acad_year_id;
          }
        }
        $acadyearSelection = [];
        foreach ($allacadyears as $key => $year) {
          if(array_key_exists($year->id, $acadyearStds)){
            array_push($acadyearSelection, $year);
          }
        }

      ?>
         <div class="form-group col-md-4 col-md-offset-2">
          <select class="form-control select" id="acadyearId" onchange="get_fee_summary_amount('<?php echo $student_id ?>',this.value)" >
            <?php foreach ($acadyearSelection as $key => $val) { ?>
              <option <?php if($this->acad_year->getAcadYearId() == $val->id) echo "selected"; ?> value="<?php echo $val->id ?>"><?php echo $val->acad_year ?></option>
            <?php } ?>
          </select>
        </div>

      <div class="loadingClassNew"></div>
        <div id="myfirstchart1" style="height: 150px;">
      </div>
      <div id="fee_summary_details">
      </div>

       

    </div>
</div>


<style type="text/css">
  .borderlesstBody tr td {
    border: none !important;
    padding: 4px !important;
  }
   .borderlesstBody tr th {
    border: none !important;
    padding: 4px !important;
  }

</style>

<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.css">
<script src="//cdnjs.cloudflare.com/ajax/libs/raphael/2.1.0/raphael-min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.min.js"></script>

<style type="text/css">
    #feeWidget span{
        line-height: 40px;
    }
    .loadingClassNew {
      border: 8px solid #eee;
      border-top: 8px solid #7193be;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      position: fixed;
      z-index: 1;
      animation: spin 2s linear infinite;
      margin-top: 35%;
      margin-left: 40%;
      position: absolute;
      z-index: 99999;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

</style>
<script>
$(document).ready(function(){
    var acadyearId = $('#acadyearId').val();
    var stdId = '<?php echo $student_id ?>';
    get_fee_summary_amount(stdId, acadyearId);
});

var gData = [];
function get_fee_summary_amount(stdId, acadyearId) {
    gData = [];
    $('.loadingClassNew').show();
    $("#myfirstchart1").html('');
    $.ajax({
        url: '<?php echo site_url('parent_controller/get_student_fee_summary_details'); ?>',
        type: 'post',
        data: {'stdId':stdId,'acadyearId':acadyearId},
        success: function(data) {
            var rData = JSON.parse(data);
            console.log(rData)
             $('.loadingClassNew').hide();
            gData.push(
              { label: 'Total fee', value:  rData.fee_amount, },
              { label: 'Fee paid', value:  rData.paid_amount, }, 
            );

            if (rData.concession != 0) {
              gData.push({ label: 'Concession', value: rData.concession, });
            }
            if (rData.adjustment != 0) {
              gData.push({ label: 'Adjustment', value: rData.adjustment, });
            }
            if (rData.discount != 0) {
              gData.push({ label: 'Discount', value: rData.discount, });
            }
            if (rData.total_fine_amount != 0) {
              gData.push({ label: 'Total Fine', value: rData.total_fine_amount, });
            }
            if (rData.total_fine_waived != 0) {
              gData.push({ label: 'Total Fine Waived', value: rData.total_fine_waived, });
            }
            if (rData.loan_provider_charges != 0) {
              gData.push({ label: 'Loan Provider Charges', value: rData.loan_provider_charges, });
            }
            if (rData.refund_amount != 0) {
              gData.push({ label: 'Refund', value: rData.refund_amount, });
            }
            new Morris.Donut({   
              element: 'myfirstchart1',
              data: gData,
              colors:['#FF0000','#00FFFF','#0000FF','#00008B','#ADD8E6','#800080','#FFFF00',"#00FF00","#FF00FF"],
                formatter: function (y) { return new Intl.NumberFormat('en-IN',{ style: 'currency', currency: 'INR' }).format(y) }
            });
        }
    });
    get_fee_summary_details(stdId, acadyearId);
}

function get_fee_summary_details(stdId, acadyearId) {
  $.ajax({
    url: '<?php echo site_url('parent_controller/fee_detailed_summary'); ?>',
    type: 'post',
    data: {'stdId':stdId,'acadyearId':acadyearId},
    success: function(data) {
      var rData = JSON.parse(data);
      if(rData.length!=0){
        $('#fee_summary_details').html(construct_summary_table(rData));
      }else{
        $('#fee_summary_details').html('<div class="no-data-display">Fees Not Assigned</div>');
      }
    }
  });
}

function construct_summary_table(rData) {
  var html = '';
  for (var i = 0; i < rData.length; i++) {
      html +='<div class="col-md-12 box" style="border: 1px solid #ccc; border-radius: 30px;margin-bottom: 0.6rem;">';
      html +='<button class="btn btn btn-primary" style="margin-left: -15px;border-top-left-radius: 60px;border-bottom-right-radius: 60px;padding: 4px 10px;">'+rData[i].blueprint_name+'</button>';
      var btnColor = 'btn-danger';
      var payemntStatus = 'Not Started';
      if (rData[i].payment_status == 'FULL') {
        btnColor = 'btn-success';
        payemntStatus = 'Fully Paid';
      }else if(rData[i].payment_status =='PARTIAL') {
        btnColor = 'btn-warning';
        payemntStatus = 'Partially Paid';
      }else{
        btnColor = 'btn-danger';
        payemntStatus = 'Not Started';
      }
      html +='<button class="btn '+btnColor+'" style="border-top-left-radius: 0px;border-bottom-right-radius: 0px;padding: 4px 10px;float: right;margin-right: -8px;"> '+payemntStatus+' </button>';
      html +='<table class="table borderless">';
      html +='<tr>';
      html +='<th>Total Fee</th>';
      html +='<td>'+numberToCurrency(rData[i].total_fee)+'</td>';
      html +='</tr>';
      html +='<tr>';
      html +='<th>Paid Amount</th>';
      html +='<td>'+numberToCurrency(rData[i].total_fee_paid)+'</td>';
      html +='</tr>';

      if (rData[i].total_concession != 0) {
        html +='<tr>';
        html +='<th>Concession</th>';
        html +='<td>'+numberToCurrency(rData[i].total_concession)+'</td>';
        html +='</tr>';
      } 
      if (rData[i].total_adjustment != 0) {        
        html +='<tr>';
        html +='<th>Adjustment</th>';
        html +='<td>'+numberToCurrency(rData[i].total_adjustment)+'</td>';
        html +='</tr>';
      }
     
      if (rData[i].discount != 0) {        
        html +='<tr>';
        html +='<th>Discount</th>';
        html +='<td>'+numberToCurrency(rData[i].discount)+'</td>';
        html +='</tr>';
      }

      if (rData[i].refund_amount != 0) {        
        html +='<tr>';
        html +='<th>Refund</th>';
        html +='<td>'+numberToCurrency(rData[i].refund_amount)+'</td>';
        html +='</tr>';
      }
      html +='<tr>';
      html +='<th>Balance</th>';
      html +='<td>'+rData[i].balance+'</td>';
      html +='</tr>';

      if (rData[i].total_fine != 0) { 
        html +='<tr>';
        html +='<th>Total Fine</th>';
        html +='<td>'+numberToCurrency(rData[i].total_fine)+'</td>';
        html +='</tr>';
      }
      var overallbalance = 0;
      overallbalance = parseFloat(rData[i].balance)  +  parseFloat(rData[i].total_fine);
      if (rData[i].total_fine != 0) {
        html +='<tr>';
        html +='<th>Over All Balance</th>';
        html +='<td>'+numberToCurrency(overallbalance)+'</td>';
        html +='</tr>';
      }

      html +='</table>';
      html +='</div>';
  }
  return html;
}
</script>


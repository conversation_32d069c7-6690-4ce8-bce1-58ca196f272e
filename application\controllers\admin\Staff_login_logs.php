<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Staff Login Logs Controller
 * 
 * Admin controller for viewing and managing staff login logs
 * 
 * @package    CodeIgniter
 * @subpackage Controllers
 * @category   Admin
 * <AUTHOR> Name
 * @version    1.0
 */
class Staff_login_logs extends CI_Controller {

    public function __construct() {
        parent::__construct();
        
        // Check if user is logged in and has admin access
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        
        // Load required libraries and models
        $this->load->library('staff_login_logger');
        $this->load->model('staff_login_model');
        $this->load->helper('staff_login_helper');
    }

    /**
     * Index page - show login logs dashboard
     */
    public function index() {
        $data['title'] = 'Staff Login Logs';
        $data['stats'] = $this->staff_login_model->get_dashboard_stats(7);
        $data['active_sessions'] = $this->staff_login_model->get_active_sessions();
        $data['suspicious_logins'] = $this->staff_login_model->get_suspicious_logins(7);
        
        // Get recent login logs
        $filters = array();
        $data['logs'] = $this->staff_login_model->get_login_logs($filters, 20, 0);
        
        $this->_render_page('admin/staff_login_logs/dashboard', $data);
    }

    /**
     * View all login logs with filtering and pagination
     */
    public function logs() {
        $data['title'] = 'Staff Login Logs';
        
        // Get filters from GET parameters
        $filters = array(
            'user_id' => $this->input->get('user_id'),
            'username' => $this->input->get('username'),
            'ip_address' => $this->input->get('ip_address'),
            'date_from' => $this->input->get('date_from'),
            'date_to' => $this->input->get('date_to'),
            'login_method' => $this->input->get('login_method'),
            'device_type' => $this->input->get('device_type'),
            'is_active' => $this->input->get('is_active')
        );
        
        // Clean up empty filters
        foreach ($filters as $key => $value) {
            if ($value === null || $value === '') {
                unset($filters[$key]);
            }
        }
        
        // Pagination
        $limit = $this->input->get('limit') ? $this->input->get('limit') : 50;
        $offset = $this->input->get('offset') ? $this->input->get('offset') : 0;
        
        $data['logs'] = $this->staff_login_model->get_login_logs($filters, $limit, $offset);
        $data['filters'] = $filters;
        
        $this->_render_page('admin/staff_login_logs/logs', $data);
    }

    /**
     * View active sessions
     */
    public function active_sessions() {
        $data['title'] = 'Active Staff Sessions';
        $data['active_sessions'] = $this->staff_login_model->get_active_sessions();
        
        $this->_render_page('admin/staff_login_logs/active_sessions', $data);
    }

    /**
     * View user login history
     * 
     * @param int $user_id User ID
     */
    public function user_history($user_id = null) {
        if (!$user_id) {
            redirect('admin/staff_login_logs', 'refresh');
        }
        
        $data['title'] = 'User Login History';
        $data['user_id'] = $user_id;
        
        // Get user details
        $this->load->model('ion_auth_model');
        $data['user'] = $this->ion_auth_model->user($user_id)->row();
        
        if (!$data['user']) {
            $this->session->set_flashdata('flashError', 'User not found');
            redirect('admin/staff_login_logs', 'refresh');
        }
        
        $data['history'] = $this->staff_login_model->get_user_history($user_id, 100);
        
        $this->_render_page('admin/staff_login_logs/user_history', $data);
    }

    /**
     * Force logout a user's active sessions
     * 
     * @param int $user_id User ID
     */
    public function force_logout($user_id = null) {
        if (!$user_id) {
            $this->session->set_flashdata('flashError', 'User ID is required');
            redirect('admin/staff_login_logs/active_sessions', 'refresh');
        }
        
        $result = $this->staff_login_model->force_logout_user($user_id, 'admin_forced');
        
        if ($result) {
            $this->session->set_flashdata('flashSuccess', 'User sessions have been terminated');
        } else {
            $this->session->set_flashdata('flashError', 'Failed to terminate user sessions');
        }
        
        redirect('admin/staff_login_logs/active_sessions', 'refresh');
    }

    /**
     * Export login logs to CSV
     */
    public function export_csv() {
        // Get filters from GET parameters
        $filters = array(
            'user_id' => $this->input->get('user_id'),
            'username' => $this->input->get('username'),
            'ip_address' => $this->input->get('ip_address'),
            'date_from' => $this->input->get('date_from'),
            'date_to' => $this->input->get('date_to'),
            'login_method' => $this->input->get('login_method'),
            'device_type' => $this->input->get('device_type'),
            'is_active' => $this->input->get('is_active')
        );
        
        // Clean up empty filters
        foreach ($filters as $key => $value) {
            if ($value === null || $value === '') {
                unset($filters[$key]);
            }
        }
        
        $csv_content = $this->staff_login_model->export_to_csv($filters);
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="staff_login_logs_' . date('Y-m-d') . '.csv"');
        
        echo $csv_content;
        exit;
    }

    /**
     * Configuration settings
     */
    public function config() {
        $data['title'] = 'Login Logging Configuration';
        
        if ($this->input->post()) {
            // Update configuration
            $config_keys = array(
                'log_retention_days',
                'track_ip_location',
                'log_user_agent_details',
                'auto_close_inactive_sessions',
                'session_timeout_minutes',
                'enable_concurrent_session_limit',
                'max_concurrent_sessions'
            );
            
            foreach ($config_keys as $key) {
                if ($this->input->post($key) !== null) {
                    $this->staff_login_model->set_config($key, $this->input->post($key));
                }
            }
            
            $this->session->set_flashdata('flashSuccess', 'Configuration updated successfully');
            redirect('admin/staff_login_logs/config', 'refresh');
        }
        
        // Get current configuration
        $config_keys = array(
            'log_retention_days',
            'track_ip_location',
            'log_user_agent_details',
            'auto_close_inactive_sessions',
            'session_timeout_minutes',
            'enable_concurrent_session_limit',
            'max_concurrent_sessions'
        );
        
        $data['config'] = array();
        foreach ($config_keys as $key) {
            $data['config'][$key] = $this->staff_login_model->get_config($key);
        }
        
        $this->_render_page('admin/staff_login_logs/config', $data);
    }

    /**
     * Clean up old logs
     */
    public function cleanup() {
        $days = $this->input->get('days') ? $this->input->get('days') : 90;
        
        $deleted_count = $this->staff_login_model->cleanup_old_logs($days);
        
        $this->session->set_flashdata('flashSuccess', "Cleaned up {$deleted_count} old login logs");
        redirect('admin/staff_login_logs', 'refresh');
    }

    /**
     * Test the login logging system
     * This is for development/testing only
     */
    public function test() {
        // Only allow in development environment
        if (ENVIRONMENT !== 'development') {
            show_error('This function is only available in development environment');
        }
        
        $output = '';
        
        // Test 1: Log a manual login
        $user_id = $this->session->userdata('user_id');
        $username = $this->session->userdata('username');
        
        if (!$user_id || !$username) {
            $output .= "Test failed: You must be logged in to run this test\n";
        } else {
            // Log a test login
            $this->load->library('staff_login_logger');
            $login_id = $this->staff_login_logger->log_login($user_id, $username, 'test');
            
            if ($login_id) {
                $output .= "Test 1 passed: Successfully logged a test login with ID {$login_id}\n";
                
                // Test 2: Track session IP
                $result = $this->staff_login_logger->track_session_ip();
                $output .= "Test 2 " . ($result ? "passed" : "failed") . ": Session IP tracking\n";
                
                // Test 3: Log a logout
                $result = $this->staff_login_logger->log_logout($user_id, 'test');
                $output .= "Test 3 " . ($result ? "passed" : "failed") . ": Logout logging\n";
            } else {
                $output .= "Test 1 failed: Could not log a test login\n";
            }
        }
        
        // Test 4: Get login statistics
        $stats = $this->staff_login_model->get_dashboard_stats(7);
        $output .= "Test 4 " . ($stats ? "passed" : "failed") . ": Get dashboard statistics\n";
        
        // Test 5: Get active sessions
        $active_sessions = $this->staff_login_model->get_active_sessions();
        $output .= "Test 5 passed: Found " . count($active_sessions) . " active sessions\n";
        
        // Display test results
        $data['title'] = 'Login Logging System Test';
        $data['output'] = $output;
        $data['user_id'] = $user_id;
        $data['username'] = $username;
        $data['ip_address'] = $this->input->ip_address();
        $data['user_agent'] = $this->input->user_agent();
        $data['session_id'] = session_id();
        
        $this->_render_page('admin/staff_login_logs/test', $data);
    }
}

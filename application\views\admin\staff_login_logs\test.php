<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?php echo $title; ?></h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> Staff Login Logging System Test</h5>
                        This test verifies that the staff login logging system is working correctly.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>Current Session Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>User ID:</strong></td>
                                    <td><?php echo $user_id ?: 'Not available'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td><?php echo $username ?: 'Not available'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>IP Address:</strong></td>
                                    <td><?php echo $ip_address; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>User Agent:</strong></td>
                                    <td><?php echo substr($user_agent, 0, 100) . (strlen($user_agent) > 100 ? '...' : ''); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Session ID:</strong></td>
                                    <td><?php echo $session_id; ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Test Results</h5>
                            <div class="alert alert-secondary">
                                <pre><?php echo $output; ?></pre>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Helper Functions Test</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Current Login Info</h6>
                                    <?php 
                                    $this->load->helper('staff_login_helper');
                                    $current_login = get_current_user_login_info();
                                    if ($current_login): ?>
                                        <table class="table table-sm table-bordered">
                                            <tr><td>Login ID:</td><td><?php echo $current_login['id']; ?></td></tr>
                                            <tr><td>Login Time:</td><td><?php echo $current_login['login_time']; ?></td></tr>
                                            <tr><td>IP Address:</td><td><?php echo $current_login['ip_address']; ?></td></tr>
                                            <tr><td>Device Type:</td><td><?php echo $current_login['device_type']; ?></td></tr>
                                            <tr><td>Browser:</td><td><?php echo $current_login['browser_name']; ?></td></tr>
                                        </table>
                                    <?php else: ?>
                                        <p class="text-muted">No current login information found</p>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <h6>Session Duration</h6>
                                    <?php $duration = get_session_duration(); ?>
                                    <p>Current session duration: <strong><?php echo $duration ? $duration . ' minutes' : 'N/A'; ?></strong></p>
                                    
                                    <h6>Active Sessions Count</h6>
                                    <?php $active_sessions = get_user_active_sessions(); ?>
                                    <p>Active sessions for current user: <strong><?php echo count($active_sessions); ?></strong></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Database Tables Check</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>staff_login_logs table</h6>
                                    <?php 
                                    $table_exists = $this->db->table_exists('staff_login_logs');
                                    if ($table_exists): 
                                        $count = $this->db->count_all('staff_login_logs');
                                    ?>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check"></i> Table exists with <?php echo $count; ?> records
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-danger">
                                            <i class="fas fa-times"></i> Table does not exist
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <h6>staff_login_config table</h6>
                                    <?php 
                                    $config_table_exists = $this->db->table_exists('staff_login_config');
                                    if ($config_table_exists): 
                                        $config_count = $this->db->count_all('staff_login_config');
                                    ?>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check"></i> Config table exists with <?php echo $config_count; ?> records
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-danger">
                                            <i class="fas fa-times"></i> Config table does not exist
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Recent Login Logs (Last 10)</h5>
                            <?php if ($table_exists): ?>
                                <?php 
                                $recent_logs = $this->db->select('id, user_id, username, ip_address, login_time, logout_time, device_type, browser_name, is_active')
                                                       ->order_by('login_time', 'DESC')
                                                       ->limit(10)
                                                       ->get('staff_login_logs')
                                                       ->result_array();
                                ?>
                                <?php if (!empty($recent_logs)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>User ID</th>
                                                    <th>Username</th>
                                                    <th>IP Address</th>
                                                    <th>Login Time</th>
                                                    <th>Logout Time</th>
                                                    <th>Device</th>
                                                    <th>Browser</th>
                                                    <th>Active</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_logs as $log): ?>
                                                    <tr>
                                                        <td><?php echo $log['id']; ?></td>
                                                        <td><?php echo $log['user_id']; ?></td>
                                                        <td><?php echo $log['username']; ?></td>
                                                        <td><?php echo $log['ip_address']; ?></td>
                                                        <td><?php echo $log['login_time']; ?></td>
                                                        <td><?php echo $log['logout_time'] ?: '-'; ?></td>
                                                        <td><?php echo $log['device_type'] ?: '-'; ?></td>
                                                        <td><?php echo $log['browser_name'] ?: '-'; ?></td>
                                                        <td>
                                                            <?php if ($log['is_active']): ?>
                                                                <span class="badge badge-success">Active</span>
                                                            <?php else: ?>
                                                                <span class="badge badge-secondary">Inactive</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No login logs found</p>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> Cannot display logs - table does not exist
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Setup Instructions</h5>
                            <div class="alert alert-info">
                                <h6>To complete the setup:</h6>
                                <ol>
                                    <li>Run the SQL script: <code>application/db/staff_login_logs.sql</code></li>
                                    <li>Ensure hooks are enabled in <code>application/config/config.php</code></li>
                                    <li>The system will automatically start logging after the next login</li>
                                    <li>Access logs via: <code>/admin/staff_login_logs</code></li>
                                </ol>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <a href="<?php echo base_url('admin/staff_login_logs'); ?>" class="btn btn-primary">
                                    <i class="fas fa-list"></i> View All Logs
                                </a>
                                <a href="<?php echo base_url('admin/staff_login_logs/active_sessions'); ?>" class="btn btn-info">
                                    <i class="fas fa-users"></i> Active Sessions
                                </a>
                                <a href="<?php echo base_url('admin/staff_login_logs/config'); ?>" class="btn btn-secondary">
                                    <i class="fas fa-cog"></i> Configuration
                                </a>
                                <a href="<?php echo base_url('admin/staff_login_logs/test'); ?>" class="btn btn-warning">
                                    <i class="fas fa-sync"></i> Refresh Test
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table-responsive {
    font-size: 0.875rem;
}
.alert pre {
    margin-bottom: 0;
    white-space: pre-wrap;
}
</style>
